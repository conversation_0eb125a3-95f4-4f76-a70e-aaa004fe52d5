<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>滚动功能测试</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            overflow: hidden; /* 防止body滚动 */
        }

        .lottery-container {
            height: 100vh;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            padding: 20px 15px;
            box-sizing: border-box;
            overflow-y: auto; /* 启用垂直滚动 */
            overflow-x: hidden;
            -webkit-overflow-scrolling: touch; /* iOS平滑滚动 */
        }

        .merchant-header {
            text-align: center;
            margin-bottom: 30px;
        }

        .merchant-name {
            font-size: 18px;
            font-weight: bold;
            color: #fff;
            margin-bottom: 5px;
        }

        .activity-name {
            font-size: 14px;
            color: rgba(255, 255, 255, 0.8);
        }

        .lottery-grid-container {
            display: flex;
            justify-content: center;
            margin-bottom: 20px;
        }

        .grid-wrapper {
            width: 300px;
            height: 300px;
        }

        .lottery-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            grid-template-rows: repeat(3, 1fr);
            gap: 4px;
            width: 100%;
            height: 100%;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            padding: 10px;
        }

        .grid-item {
            background: rgba(255, 255, 255, 0.9);
            border-radius: 8px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            position: relative;
            transition: all 0.3s ease;
            box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
        }

        .grid-item.center {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: #fff;
        }

        .prize-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            height: 100%;
            text-align: center;
            padding: 5px;
        }

        .prize-icon {
            font-size: 20px;
            margin-bottom: 4px;
        }

        .prize-name {
            font-size: 10px;
            color: #333;
            line-height: 1.2;
        }

        .center-button {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            height: 100%;
            color: #fff;
        }

        .center-text {
            font-size: 12px;
            font-weight: bold;
            margin-bottom: 4px;
        }

        .remaining-text {
            font-size: 8px;
            opacity: 0.9;
        }

        .section {
            background: rgba(255, 255, 255, 0.9);
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 15px;
        }

        .section-title {
            font-size: 16px;
            font-weight: bold;
            color: #333;
            margin-bottom: 10px;
            text-align: center;
        }

        .section-content {
            font-size: 14px;
            color: #666;
            line-height: 1.6;
            text-align: center;
        }

        .test-item {
            background: #f8f9fa;
            border-radius: 5px;
            padding: 10px;
            margin-bottom: 10px;
            border-left: 4px solid #667eea;
        }

        .test-item h4 {
            color: #333;
            margin-bottom: 5px;
        }

        .test-item p {
            color: #666;
            font-size: 12px;
            line-height: 1.4;
        }

        .footer-section {
            background: rgba(255, 255, 255, 0.9);
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
            text-align: center;
        }

        .footer-section h3 {
            color: #333;
            margin-bottom: 10px;
        }

        .footer-section p {
            color: #666;
            font-size: 14px;
        }

        .scroll-indicator {
            position: fixed;
            top: 50%;
            right: 10px;
            transform: translateY(-50%);
            background: rgba(255, 255, 255, 0.8);
            padding: 5px;
            border-radius: 15px;
            font-size: 12px;
            color: #333;
            z-index: 1000;
        }
    </style>
</head>
<body>
    <div class="scroll-indicator">
        滚动测试
    </div>
    
    <div class="lottery-container" id="scrollContainer">
        <!-- 商家信息 -->
        <div class="merchant-header">
            <div class="merchant-name">滚动功能测试页面</div>
            <div class="activity-name">测试纵向滚动是否正常工作</div>
        </div>

        <!-- 九宫格抽奖 -->
        <div class="lottery-grid-container">
            <div class="grid-wrapper">
                <div class="lottery-grid">
                    <div class="grid-item">
                        <div class="prize-item">
                            <div class="prize-icon">🎁</div>
                            <div class="prize-name">一等奖</div>
                        </div>
                    </div>
                    <div class="grid-item">
                        <div class="prize-item">
                            <div class="prize-icon">🎁</div>
                            <div class="prize-name">二等奖</div>
                        </div>
                    </div>
                    <div class="grid-item">
                        <div class="prize-item">
                            <div class="prize-icon">🎁</div>
                            <div class="prize-name">三等奖</div>
                        </div>
                    </div>
                    <div class="grid-item">
                        <div class="prize-item">
                            <div class="prize-icon">🎁</div>
                            <div class="prize-name">四等奖</div>
                        </div>
                    </div>
                    <div class="grid-item center">
                        <div class="center-button">
                            <div class="center-text">点击抽奖</div>
                            <div class="remaining-text">剩余3次</div>
                        </div>
                    </div>
                    <div class="grid-item">
                        <div class="prize-item">
                            <div class="prize-icon">🎁</div>
                            <div class="prize-name">五等奖</div>
                        </div>
                    </div>
                    <div class="grid-item">
                        <div class="prize-item">
                            <div class="prize-icon">🎁</div>
                            <div class="prize-name">六等奖</div>
                        </div>
                    </div>
                    <div class="grid-item">
                        <div class="prize-item">
                            <div class="prize-icon">🎁</div>
                            <div class="prize-name">七等奖</div>
                        </div>
                    </div>
                    <div class="grid-item">
                        <div class="prize-item">
                            <div class="prize-icon">🎁</div>
                            <div class="prize-name">谢谢参与</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 测试内容区域 -->
        <div class="section">
            <div class="section-title">滚动测试区域 1</div>
            <div class="section-content">
                这是第一个测试区域。如果您能看到这个内容，说明页面正常加载。
            </div>
        </div>

        <div class="section">
            <div class="section-title">抽奖次数信息</div>
            <div class="test-item">
                <h4>今日限制</h4>
                <p>今日已抽奖 0/5 次，剩余 5 次</p>
            </div>
            <div class="test-item">
                <h4>总限制</h4>
                <p>总共已抽奖 0/10 次，剩余 10 次</p>
            </div>
        </div>

        <div class="section">
            <div class="section-title">奖品设置</div>
            <div class="test-item">
                <h4>一等奖</h4>
                <p>中奖率：5%</p>
            </div>
            <div class="test-item">
                <h4>二等奖</h4>
                <p>中奖率：10%</p>
            </div>
            <div class="test-item">
                <h4>三等奖</h4>
                <p>中奖率：15%</p>
            </div>
            <div class="test-item">
                <h4>四等奖</h4>
                <p>中奖率：20%</p>
            </div>
        </div>

        <div class="section">
            <div class="section-title">我的记录</div>
            <div class="test-item">
                <h4>2024-01-15 14:30</h4>
                <p>获得：三等奖 - 已领取</p>
            </div>
            <div class="test-item">
                <h4>2024-01-15 14:25</h4>
                <p>获得：谢谢参与 - 未中奖</p>
            </div>
            <div class="test-item">
                <h4>2024-01-15 14:20</h4>
                <p>获得：五等奖 - 待领取</p>
            </div>
            <div class="test-item">
                <h4>2024-01-15 14:15</h4>
                <p>获得：谢谢参与 - 未中奖</p>
            </div>
            <div class="test-item">
                <h4>2024-01-15 14:10</h4>
                <p>获得：二等奖 - 已领取</p>
            </div>
        </div>

        <div class="footer-section">
            <h3>滚动测试完成</h3>
            <p>如果您能看到这个底部内容，说明滚动功能正常工作！</p>
            <p>页面可以正常纵向滚动，数据显示完整。</p>
        </div>
    </div>

    <script>
        // 添加滚动监听，显示当前滚动位置
        const container = document.getElementById('scrollContainer');
        const indicator = document.querySelector('.scroll-indicator');
        
        container.addEventListener('scroll', function() {
            const scrollTop = container.scrollTop;
            const scrollHeight = container.scrollHeight;
            const clientHeight = container.clientHeight;
            const scrollPercent = Math.round((scrollTop / (scrollHeight - clientHeight)) * 100);
            
            indicator.textContent = `滚动: ${scrollPercent}%`;
        });
        
        // 页面加载完成后显示提示
        window.addEventListener('load', function() {
            setTimeout(() => {
                alert('滚动测试页面加载完成！\n\n请尝试向下滚动查看所有内容。\n如果能看到底部的"滚动测试完成"信息，说明滚动功能正常。');
            }, 500);
        });
    </script>
</body>
</html>
