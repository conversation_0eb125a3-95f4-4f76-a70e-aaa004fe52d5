<template>
  <scroll-view class="scroll-container" scroll-y="true" enable-back-to-top="true" :scroll-with-animation="true">
    <view class="content">
      <view class="header">滚动测试页面</view>
      
      <view class="section" v-for="i in 20" :key="i">
        <view class="section-title">第 {{ i }} 个区块</view>
        <view class="section-content">
          <text>这是第 {{ i }} 个测试区块的内容。</text>
          <text>用于测试页面的纵向滚动功能是否正常工作。</text>
          <text>如果您能看到这些内容并且可以滚动查看所有区块，说明滚动功能正常。</text>
        </view>
      </view>
      
      <view class="footer">
        <text>如果您能看到这个底部内容，说明滚动功能正常工作！</text>
      </view>
    </view>
  </scroll-view>
</template>

<script>
export default {
  data() {
    return {}
  }
}
</script>

<style lang="scss" scoped>
.scroll-container {
  height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  box-sizing: border-box;
}

.content {
  padding: 40rpx 30rpx;
}

.header {
  font-size: 36rpx;
  font-weight: bold;
  color: #fff;
  text-align: center;
  margin-bottom: 40rpx;
}

.section {
  background: rgba(255, 255, 255, 0.9);
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  
  .section-title {
    font-size: 32rpx;
    font-weight: bold;
    color: #333;
    margin-bottom: 20rpx;
  }
  
  .section-content {
    text {
      display: block;
      font-size: 28rpx;
      color: #666;
      line-height: 1.6;
      margin-bottom: 10rpx;
    }
  }
}

.footer {
  background: rgba(255, 255, 255, 0.9);
  border-radius: 20rpx;
  padding: 40rpx;
  text-align: center;
  margin-bottom: 40rpx;
  
  text {
    font-size: 30rpx;
    color: #333;
    font-weight: bold;
  }
}
</style>
