<template>
  <view class="test-container">
    <view class="test-title">奖品图片显示测试</view>

    <!-- 九宫格测试 -->
    <view class="section">
      <view class="section-title">九宫格奖品显示</view>
      <view class="lottery-grid-container">
        <view class="grid-wrapper">
          <view class="lottery-grid">
            <view class="grid-item" v-for="(item, index) in testGridItems" :key="index"
              :class="{ 'center': index === 4 }">
              <view v-if="index === 4" class="center-button">
                <view class="center-text">点击抽奖</view>
              </view>
              <view v-else-if="item" class="prize-item">
                <view class="prize-icon">
                  <image v-if="item.prizeImage" :src="getFullImageUrl(item.prizeImage)" class="prize-image"
                    mode="aspectFit" />
                  <text v-else class="default-icon">🎁</text>
                </view>
                <view class="prize-name">{{ item.prizeName }}</view>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 奖品列表测试 -->
    <view class="section">
      <view class="section-title">奖品列表显示</view>
      <view class="prize-list">
        <view class="prize-items">
          <view class="prize-item" v-for="(prize, index) in testPrizeList" :key="index">
            <view class="prize-info">
              <view class="prize-icon-small">
                <image v-if="prize.prizeImage" :src="getFullImageUrl(prize.prizeImage)" class="prize-image-small"
                  mode="aspectFit" />
                <text v-else class="default-icon-small">🎁</text>
              </view>
              <view class="prize-details">
                <view class="prize-name">{{ prize.prizeName }}</view>
                <view class="prize-probability">中奖率：{{ prize.probability }}%</view>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import { getImageUrl } from '@/utils/api.js'

export default {
  data() {
    return {
      // 测试用的奖品数据，包含相对路径图片
      testPrizeList: [
        {
          prizeName: "一等奖",
          prizeType: "physical",
          probability: 5,
          prizeImage: "/static/images/prizes/prize1.jpg" // 后端相对路径
        },
        {
          prizeName: "二等奖",
          prizeType: "coupon",
          probability: 15,
          prizeImage: "/static/images/prizes/prize2.jpg" // 后端相对路径
        },
        {
          prizeName: "三等奖",
          prizeType: "physical",
          probability: 20,
          prizeImage: "static/images/prizes/prize3.jpg" // 不带/的相对路径
        },
        {
          prizeName: "四等奖",
          prizeType: "coupon",
          probability: 10,
          prizeImage: "https://via.placeholder.com/100x100/45b7d1/ffffff?text=四等奖" // 完整URL测试
        },
        {
          prizeName: "谢谢参与",
          prizeType: "thanks",
          probability: 55,
          prizeImage: "" // 没有图片，使用默认图标
        }
      ],
      testGridItems: []
    }
  },

  onLoad() {
    this.initTestGrid()
  },

  methods: {
    initTestGrid() {
      // 创建九宫格测试数据
      this.testGridItems = []
      const prizes = [...this.testPrizeList]

      // 填充到8个位置
      while (prizes.length < 8) {
        prizes.push({
          prizeName: '谢谢参与',
          prizeType: 'thanks',
          probability: 0,
          prizeImage: ''
        })
      }

      // 创建9个位置，中间是抽奖按钮
      for (let i = 0; i < 9; i++) {
        if (i === 4) {
          this.testGridItems.push(null) // 中心抽奖按钮
        } else {
          const prizeIndex = i < 4 ? i : i - 1
          this.testGridItems.push(prizes[prizeIndex] || {
            prizeName: '谢谢参与',
            prizeType: 'thanks',
            probability: 0,
            prizeImage: ''
          })
        }
      }
    },

    // 获取完整的图片URL
    getFullImageUrl(imagePath) {
      return getImageUrl(imagePath)
    }
  }
}
</script>

<style lang="scss" scoped>
.test-container {
  padding: 40rpx 30rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  min-height: 100vh;
}

.test-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #fff;
  text-align: center;
  margin-bottom: 40rpx;
}

.section {
  margin-bottom: 40rpx;
}

.section-title {
  font-size: 28rpx;
  color: #fff;
  margin-bottom: 20rpx;
  text-align: center;
}

/* 九宫格样式 - 复制自主页面 */
.lottery-grid-container {
  display: flex;
  justify-content: center;
  margin-bottom: 40rpx;

  .grid-wrapper {
    width: 600rpx;
    height: 600rpx;
  }

  .lottery-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    grid-template-rows: repeat(3, 1fr);
    gap: 8rpx;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 20rpx;
    padding: 20rpx;
    box-sizing: border-box;
  }

  .grid-item {
    background: rgba(255, 255, 255, 0.9);
    border-radius: 15rpx;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    position: relative;
    transition: all 0.3s ease;
    box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);

    &.center {
      background: linear-gradient(135deg, #667eea, #764ba2);
    }
  }

  .prize-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    text-align: center;
    padding: 10rpx;

    .prize-icon {
      font-size: 48rpx;
      margin-bottom: 8rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      width: 60rpx;
      height: 60rpx;

      .prize-image {
        width: 100%;
        height: 100%;
        border-radius: 8rpx;
      }

      .default-icon {
        font-size: 48rpx;
      }
    }

    .prize-name {
      font-size: 24rpx;
      color: #333;
      line-height: 1.2;
      word-break: break-all;
    }
  }

  .center-button {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    color: #fff;

    .center-text {
      font-size: 28rpx;
      font-weight: bold;
    }
  }
}

/* 奖品列表样式 - 复制自主页面 */
.prize-list {
  background: rgba(255, 255, 255, 0.9);
  border-radius: 20rpx;
  padding: 30rpx;

  .prize-items {
    display: flex;
    flex-direction: column;
    gap: 20rpx;
  }

  .prize-item {
    background: #f8f9fa;
    border-radius: 10rpx;
    padding: 20rpx;

    .prize-info {
      display: flex;
      align-items: center;
      gap: 15rpx;
    }

    .prize-icon-small {
      width: 40rpx;
      height: 40rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      flex-shrink: 0;

      .prize-image-small {
        width: 100%;
        height: 100%;
        border-radius: 6rpx;
      }

      .default-icon-small {
        font-size: 32rpx;
      }
    }

    .prize-details {
      flex: 1;
      text-align: left;
    }

    .prize-name {
      font-size: 28rpx;
      color: #333;
      margin-bottom: 8rpx;
    }

    .prize-probability {
      font-size: 24rpx;
      color: #666;
    }
  }
}
</style>
