# 奖品图片显示功能说明

## 功能概述

九宫格抽奖页面现在支持显示后端接口传回来的奖品图片，替代了原来固定的 🎁 图标。支持后端服务器相对路径和完整 URL 两种格式。

## 修改内容

### 1. 九宫格奖品显示

**位置**: `pages/lottery/lottery.vue` 第 24-30 行

**修改前**:

```vue
<view v-else-if="item" class="prize-item">
  <view class="prize-icon">🎁</view>
  <view class="prize-name">{{ item.prizeName }}</view>
</view>
```

**修改后**:

```vue
<view v-else-if="item" class="prize-item">
  <view class="prize-icon">
    <image v-if="item.prizeImage" :src="getFullImageUrl(item.prizeImage)" class="prize-image" mode="aspectFit" />
    <text v-else class="default-icon">🎁</text>
  </view>
  <view class="prize-name">{{ item.prizeName }}</view>
</view>
```

### 2. 奖品列表显示

**位置**: `pages/lottery/lottery.vue` 第 62-73 行

**修改前**:

```vue
<view class="prize-item" v-for="(prize, index) in prizeList" :key="index">
  <view class="prize-name">{{ prize.prizeName }}</view>
  <view class="prize-probability">中奖率：{{ prize.probability }}%</view>
</view>
```

**修改后**:

```vue
<view class="prize-item" v-for="(prize, index) in prizeList" :key="index">
  <view class="prize-info">
    <view class="prize-icon-small">
      <image v-if="prize.prizeImage" :src="getFullImageUrl(prize.prizeImage)" class="prize-image-small" mode="aspectFit" />
      <text v-else class="default-icon-small">🎁</text>
    </view>
    <view class="prize-details">
      <view class="prize-name">{{ prize.prizeName }}</view>
      <view class="prize-probability">中奖率：{{ prize.probability }}%</view>
    </view>
  </view>
</view>
```

### 3. 图片 URL 处理功能

**位置**: `utils/api.js` 第 5-19 行

新增了 `getImageUrl` 函数来处理图片 URL：

```javascript
// 图片URL处理函数
export const getImageUrl = (imagePath) => {
  if (!imagePath) {
    return "";
  }

  // 如果已经是完整的URL（包含http或https），直接返回
  if (imagePath.startsWith("http://") || imagePath.startsWith("https://")) {
    return imagePath;
  }

  // 如果是相对路径，拼接基础URL
  // 确保路径以/开头
  const path = imagePath.startsWith("/") ? imagePath : "/" + imagePath;
  return API_BASE_URL + path;
};
```

**在页面中的使用**:

```javascript
// 导入函数
import { merchantApi, lotteryApi, getImageUrl } from '@/utils/api.js'

// 在methods中添加方法
methods: {
  // 获取完整的图片URL
  getFullImageUrl(imagePath) {
    return getImageUrl(imagePath)
  }
}
```

### 4. CSS 样式更新

#### 九宫格图标样式

```scss
.prize-icon {
  font-size: 48rpx;
  margin-bottom: 8rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 60rpx;
  height: 60rpx;

  .prize-image {
    width: 100%;
    height: 100%;
    border-radius: 8rpx;
  }

  .default-icon {
    font-size: 48rpx;
  }
}
```

#### 奖品列表样式

```scss
.prize-item {
  // ... 其他样式

  .prize-info {
    display: flex;
    align-items: center;
    gap: 15rpx;
  }

  .prize-icon-small {
    width: 40rpx;
    height: 40rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;

    .prize-image-small {
      width: 100%;
      height: 100%;
      border-radius: 6rpx;
    }

    .default-icon-small {
      font-size: 32rpx;
    }
  }

  .prize-details {
    flex: 1;
    text-align: left;
  }
}
```

## 数据结构

### 奖品配置数据结构

```javascript
{
  prizeName: "一等奖",           // 奖品名称
  prizeType: "physical",        // 奖品类型
  prizeValue: "100元代金券",    // 奖品价值
  probability: 5,               // 中奖概率
  totalCount: 10,              // 总库存
  dailyCount: 2,               // 每日限制
  prizeImage: "/static/images/prize1.jpg"  // 奖品图片路径 (新增字段)
}
```

### 支持的图片路径格式

1. **后端相对路径（推荐）**:

   - `/static/images/prize1.jpg` - 以/开头的绝对路径
   - `static/images/prize1.jpg` - 不以/开头的相对路径
   - 自动拼接为: `http://localhost:18080/static/images/prize1.jpg`

2. **完整 URL**:

   - `https://example.com/images/prize1.jpg` - HTTPS 完整 URL
   - `http://example.com/images/prize1.jpg` - HTTP 完整 URL
   - 直接使用，不做任何处理

3. **空值处理**:
   - `""` - 空字符串
   - `null` - 空值
   - `undefined` - 未定义
   - 显示默认 🎁 图标

## 功能特性

1. **图片优先显示**: 如果奖品配置中有 `prizeImage` 字段且不为空，则显示图片
2. **降级处理**: 如果没有图片或图片加载失败，显示默认的 🎁 图标
3. **响应式设计**: 图片会自动适应容器大小，保持比例
4. **圆角美化**: 图片添加了圆角效果，提升视觉体验

## 测试页面

创建了测试页面 `pages/test-prize-image/test-prize-image.vue` 用于验证图片显示功能。

### 访问方式

在浏览器中访问: `/pages/test-prize-image/test-prize-image`

### 测试内容

- 九宫格奖品图片显示
- 奖品列表图片显示
- 有图片和无图片的对比效果

## 注意事项

1. **图片格式**: 支持常见的图片格式 (jpg, png, gif, webp 等)
2. **图片大小**: 建议使用适当大小的图片，避免过大影响加载速度
3. **网络环境**: 图片需要能够正常访问，建议使用 CDN
4. **降级机制**: 确保在图片加载失败时有合适的降级显示

## 后端配置

在后台管理系统中，可以为每个奖品配置图片路径：

1. 进入"抽奖管理" -> "抽奖活动"
2. 编辑活动的奖品配置
3. 在"奖品图片"字段中填入图片路径
4. 保存配置

### 图片路径配置示例:

**推荐使用后端相对路径**:

- `/static/images/prizes/prize1.jpg`
- `/upload/images/2024/prize2.png`
- `static/files/prizes/gift.jpg`

**也支持完整 URL**:

- `https://example.com/images/prize1.jpg`
- `https://cdn.example.com/prizes/gift.png`

### 后端图片存储建议:

1. **静态资源目录**: 将图片放在后端项目的静态资源目录下
2. **文件上传**: 通过后台管理系统上传图片到服务器
3. **CDN 加速**: 对于高访问量场景，建议使用 CDN
4. **图片优化**: 建议使用适当大小的图片（推荐 100x100 到 200x200 像素）
